'use client';

import { createContext, ReactNode, useCallback, useContext, useState } from 'react';

// ============================================================================
// TYPES
// ============================================================================

type LoadingState = Record<string, boolean>;

interface LoadingContextType {
	// Global loading state
	isLoading: boolean;
	setLoading: (loading: boolean) => void;

	// Scoped loading states for different contexts
	loadingStates: LoadingState;
	setLoadingState: (key: string, loading: boolean) => void;
	getLoadingState: (key: string) => boolean;
	clearLoadingStates: () => void;
}

// ============================================================================
// CONTEXT
// ============================================================================

const LoadingContext = createContext<LoadingContextType>({
	isLoading: false,
	setLoading: () => {},
	loadingStates: {},
	setLoadingState: () => {},
	getLoadingState: () => false,
	clearLoadingStates: () => {},
});

// ============================================================================
// HOOKS
// ============================================================================

// Global loading hook
export const useLoading = () => {
	const context = useContext(LoadingContext);
	if (!context) throw new Error('useLoading must be used within a LoadingProvider');
	return {
		isLoading: context.isLoading,
		setLoading: context.setLoading,
	};
};

// Hook to get all loading states (for floating loading system)
export const useAllLoadingStates = () => {
	const context = useContext(LoadingContext);
	if (!context) throw new Error('useAllLoadingStates must be used within a LoadingProvider');
	return {
		isLoading: context.isLoading,
		loadingStates: context.loadingStates,
	};
};

// Scoped loading hook for specific contexts
export const useScopedLoading = (scope: string) => {
	const context = useContext(LoadingContext);
	if (!context) {
		throw new Error('useScopedLoading must be used within a LoadingProvider');
	}

	const setLoading = useCallback(
		(key: string, loading: boolean) => {
			context.setLoadingState(`${scope}.${key}`, loading);
		},
		[context, scope]
	);

	const getLoading = useCallback(
		(key: string) => {
			return context.getLoadingState(`${scope}.${key}`);
		},
		[context, scope]
	);

	return {
		setLoading,
		getLoading,
		loadingStates: context.loadingStates,
	};
};

// Utility hook for loading/error management
export const useLoadingError = (scope: string) => {
	const { setLoading: setGlobalLoading } = useLoading();
	const scopedLoading = useScopedLoading(scope);

	return useCallback(
		(
			setLoading: (loading: boolean) => void,
			setError: (error: Error | null) => void,
			key?: string
		) => {
			return {
				start: () => {
					setLoading(true);
					setError(null);
					if (key && scopedLoading) {
						scopedLoading.setLoading(key, true);
					} else {
						setGlobalLoading(true);
					}
				},
				end: (error?: Error | null) => {
					setLoading(false);
					if (error) setError(error);
					if (key && scopedLoading) {
						scopedLoading.setLoading(key, false);
					} else {
						setGlobalLoading(false);
					}
				},
				setError,
			};
		},
		[setGlobalLoading, scopedLoading]
	);
};

// ============================================================================
// PROVIDER COMPONENT
// ============================================================================

export function LoadingProvider({ children }: { children: ReactNode }) {
	const [isLoading, setIsLoading] = useState(false);
	const [loadingStates, setLoadingStates] = useState<LoadingState>({});

	const setLoading = useCallback((loading: boolean) => {
		setIsLoading(loading);
	}, []);

	const setLoadingState = useCallback((key: string, loading: boolean) => {
		setLoadingStates((prev) => ({
			...prev,
			[key]: loading,
		}));
	}, []);

	const getLoadingState = useCallback(
		(key: string) => {
			return loadingStates[key] || false;
		},
		[loadingStates]
	);

	const clearLoadingStates = useCallback(() => {
		setLoadingStates({});
	}, []);

	const contextValue: LoadingContextType = {
		isLoading,
		setLoading,
		loadingStates,
		setLoadingState,
		getLoadingState,
		clearLoadingStates,
	};

	return <LoadingContext.Provider value={contextValue}>{children}</LoadingContext.Provider>;
}
