'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { useLoading, useScopedLoading } from '@/contexts/loading-context';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { useState } from 'react';
import { Settings, MessageSquare, HelpCircle, Zap, CheckCircle, XCircle } from 'lucide-react';

export default function TestFloatingLoadingPage() {
	const { setLoading, isLoading } = useLoading();
	const [testResults, setTestResults] = useState<Record<string, 'success' | 'error' | null>>({});

	// Scoped loading examples (these will trigger global loading)
	const collectionsLoading = useScopedLoading('collections');
	const wordsLoading = useScopedLoading('words');
	const llmLoading = useScopedLoading('llm');

	// Test functions
	const simulateGlobalLoading = () => {
		setLoading(true);
		setTestResults((prev) => ({ ...prev, global: null }));
		setTimeout(() => {
			setLoading(false);
			setTestResults((prev) => ({ ...prev, global: 'success' }));
		}, 3000);
	};

	const simulateCollectionsLoading = () => {
		collectionsLoading.setLoading('fetch', true);
		setTestResults((prev) => ({ ...prev, collections: null }));
		setTimeout(() => {
			collectionsLoading.setLoading('fetch', false);
			setTestResults((prev) => ({ ...prev, collections: 'success' }));
		}, 2000);
	};

	const simulateWordsLoading = () => {
		wordsLoading.setLoading('create', true);
		setTestResults((prev) => ({ ...prev, words: null }));
		setTimeout(() => {
			wordsLoading.setLoading('create', false);
			setTestResults((prev) => ({ ...prev, words: 'success' }));
		}, 1500);
	};

	const simulateLLMLoading = () => {
		llmLoading.setLoading('generate', true);
		setTestResults((prev) => ({ ...prev, llm: null }));
		setTimeout(() => {
			llmLoading.setLoading('generate', false);
			setTestResults((prev) => ({ ...prev, llm: 'success' }));
		}, 4000);
	};

	const simulateMultipleLoading = () => {
		// Test multiple scoped loading states - all will trigger global loading
		setLoading(true);
		collectionsLoading.setLoading('fetch', true);
		wordsLoading.setLoading('create', true);
		llmLoading.setLoading('generate', true);

		setTestResults((prev) => ({
			...prev,
			global: null,
			collections: null,
			words: null,
			llm: null,
		}));

		setTimeout(() => {
			setLoading(false);
			setTestResults((prev) => ({ ...prev, global: 'success' }));
		}, 2000);

		setTimeout(() => {
			collectionsLoading.setLoading('fetch', false);
			setTestResults((prev) => ({ ...prev, collections: 'success' }));
		}, 3000);

		setTimeout(() => {
			wordsLoading.setLoading('create', false);
			setTestResults((prev) => ({ ...prev, words: 'success' }));
		}, 3500);

		setTimeout(() => {
			llmLoading.setLoading('generate', false);
			setTestResults((prev) => ({ ...prev, llm: 'success' }));
		}, 4000);
	};

	const getStatusIcon = (status: 'success' | 'error' | null) => {
		if (status === 'success') return <CheckCircle className="h-4 w-4 text-green-500" />;
		if (status === 'error') return <XCircle className="h-4 w-4 text-red-500" />;
		return null;
	};

	return (
		<div className="container mx-auto p-6 space-y-8">
			{/* Header */}
			<div className="text-center space-y-4">
				<h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
					Global Floating Loading System Test
				</h1>
				<p className="text-lg text-muted-foreground max-w-2xl mx-auto">
					Test the global floating loading system. The floating loading indicator will
					appear whenever ANY loading state is active.
				</p>
				<div className="flex items-center justify-center gap-4 text-sm">
					<Badge variant="outline" className="flex items-center gap-1">
						<Settings className="h-3 w-3" />
						Same as Settings
					</Badge>
					<Badge variant="outline" className="flex items-center gap-1">
						<MessageSquare className="h-3 w-3" />
						Same as Feedback
					</Badge>
					<Badge variant="outline" className="flex items-center gap-1">
						<HelpCircle className="h-3 w-3" />
						Same as Guidance
					</Badge>
				</div>
			</div>

			{/* Global Loading Section */}
			<Card className="border-2 border-blue-200 dark:border-blue-800">
				<CardHeader>
					<CardTitle className="flex items-center gap-2">
						<Zap className="h-5 w-5 text-blue-500" />
						Global Loading Test
						<Badge variant="secondary">Priority: Critical</Badge>
					</CardTitle>
				</CardHeader>
				<CardContent className="space-y-4">
					<p className="text-sm text-muted-foreground">
						Tests the global loading state. This will show the floating loading
						indicator.
					</p>
					<div className="flex items-center gap-4">
						<Button
							onClick={simulateGlobalLoading}
							disabled={isLoading}
							className="flex-1"
						>
							{isLoading ? (
								<>
									<LoadingSpinner size="sm" className="mr-2" />
									Loading... (3s)
								</>
							) : (
								'Start Global Loading'
							)}
						</Button>
						<div className="flex items-center gap-2">
							{getStatusIcon(testResults.global)}
							<span className="text-sm text-muted-foreground">
								Status: {isLoading ? 'Active' : 'Idle'}
							</span>
						</div>
					</div>
				</CardContent>
			</Card>

			{/* Scoped Loading Section */}
			<Card>
				<CardHeader>
					<CardTitle className="flex items-center gap-2">
						<Zap className="h-5 w-5 text-green-500" />
						Scoped Loading Tests
						<Badge variant="outline">Triggers Global Loading</Badge>
					</CardTitle>
				</CardHeader>
				<CardContent>
					<p className="text-sm text-muted-foreground mb-4">
						These scoped loading states will also trigger the global floating loading
						indicator.
					</p>
					<div className="grid grid-cols-1 md:grid-cols-3 gap-4">
						{/* Collections Loading */}
						<div className="space-y-3">
							<h4 className="font-medium">Collections Fetch</h4>
							<Button
								onClick={simulateCollectionsLoading}
								disabled={collectionsLoading.getLoading('fetch')}
								variant="outline"
								className="w-full"
							>
								{collectionsLoading.getLoading('fetch') ? (
									<>
										<LoadingSpinner size="sm" className="mr-2" />
										Loading...
									</>
								) : (
									'Fetch Collections (2s)'
								)}
							</Button>
							<div className="flex items-center gap-2 text-xs text-muted-foreground">
								{getStatusIcon(testResults.collections)}
								Status:{' '}
								{collectionsLoading.getLoading('fetch') ? 'Loading' : 'Ready'}
							</div>
						</div>

						{/* Words Loading */}
						<div className="space-y-3">
							<h4 className="font-medium">Words Create</h4>
							<Button
								onClick={simulateWordsLoading}
								disabled={wordsLoading.getLoading('create')}
								variant="outline"
								className="w-full"
							>
								{wordsLoading.getLoading('create') ? (
									<>
										<LoadingSpinner size="sm" className="mr-2" />
										Creating...
									</>
								) : (
									'Create Word (1.5s)'
								)}
							</Button>
							<div className="flex items-center gap-2 text-xs text-muted-foreground">
								{getStatusIcon(testResults.words)}
								Status: {wordsLoading.getLoading('create') ? 'Creating' : 'Ready'}
							</div>
						</div>

						{/* LLM Loading */}
						<div className="space-y-3">
							<h4 className="font-medium">LLM Generate</h4>
							<Button
								onClick={simulateLLMLoading}
								disabled={llmLoading.getLoading('generate')}
								variant="outline"
								className="w-full"
							>
								{llmLoading.getLoading('generate') ? (
									<>
										<LoadingSpinner size="sm" className="mr-2" />
										Generating...
									</>
								) : (
									'Generate Content (4s)'
								)}
							</Button>
							<div className="flex items-center gap-2 text-xs text-muted-foreground">
								{getStatusIcon(testResults.llm)}
								Status: {llmLoading.getLoading('generate') ? 'Generating' : 'Ready'}
							</div>
						</div>
					</div>
				</CardContent>
			</Card>

			{/* Multiple Loading Test */}
			<Card className="border-2 border-orange-200 dark:border-orange-800">
				<CardHeader>
					<CardTitle className="flex items-center gap-2">
						<Zap className="h-5 w-5 text-orange-500" />
						Multiple Loading Test
						<Badge variant="destructive">Advanced</Badge>
					</CardTitle>
				</CardHeader>
				<CardContent className="space-y-4">
					<p className="text-sm text-muted-foreground">
						Test multiple loading states simultaneously. The global floating loading
						will remain visible as long as ANY loading state is active.
					</p>
					<Button
						onClick={simulateMultipleLoading}
						variant="destructive"
						className="w-full"
					>
						Start Multiple Loading Test
					</Button>
					<div className="grid grid-cols-2 md:grid-cols-4 gap-2 text-xs">
						<div className="flex items-center gap-1">
							{getStatusIcon(testResults.global)}
							<span>Global</span>
						</div>
						<div className="flex items-center gap-1">
							{getStatusIcon(testResults.collections)}
							<span>Collections</span>
						</div>
						<div className="flex items-center gap-1">
							{getStatusIcon(testResults.words)}
							<span>Words</span>
						</div>
						<div className="flex items-center gap-1">
							{getStatusIcon(testResults.llm)}
							<span>LLM</span>
						</div>
					</div>
				</CardContent>
			</Card>

			{/* Documentation */}
			<Card>
				<CardHeader>
					<CardTitle>How It Works</CardTitle>
				</CardHeader>
				<CardContent className="space-y-4">
					<div className="space-y-3">
						<h4 className="font-semibold">✅ Single Global Loading Indicator</h4>
						<p className="text-sm text-muted-foreground">
							Only ONE floating loading indicator appears, regardless of how many
							loading states are active.
						</p>
					</div>
					<Separator />
					<div className="space-y-3">
						<h4 className="font-semibold">✅ Triggered by ANY Loading State</h4>
						<p className="text-sm text-muted-foreground">
							The global floating loading appears when either global loading OR any
							scoped loading state is true.
						</p>
					</div>
					<Separator />
					<div className="space-y-3">
						<h4 className="font-semibold">✅ Uses Existing Floating System</h4>
						<p className="text-sm text-muted-foreground">
							Uses the same floating notification system as settings, feedback, and
							guidance components.
						</p>
					</div>
					<Separator />
					<div className="space-y-3">
						<h4 className="font-semibold">Implementation</h4>
						<div className="bg-muted p-4 rounded-lg">
							<code className="text-sm">
								{`// Global loading automatically shows when ANY loading state is true
const { setLoading } = useLoading();
setLoading(true); // Shows floating loading

// Scoped loading also triggers global loading
const scoped = useScopedLoading('scope');
scoped.setLoading('key', true); // Also shows floating loading`}
							</code>
						</div>
					</div>
				</CardContent>
			</Card>
		</div>
	);
}
