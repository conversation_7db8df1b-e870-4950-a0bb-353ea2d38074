'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
	useFloatingLoading,
	useFloatingScopedLoading,
	useCustomFloatingLoading,
} from '@/components/floating-ui';
import { useLoading } from '@/contexts/loading-context';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { useState } from 'react';

export default function TestFloatingLoadingPage() {
	const { setLoading } = useLoading();
	const [customVisible, setCustomVisible] = useState(false);

	// Global floating loading
	const globalLoading = useFloatingLoading({
		size: 'md',
	});

	// Scoped floating loading
	const scopedLoading = useFloatingScopedLoading('test', 'operation', {
		size: 'lg',
	});

	// Custom floating loading - still using custom content
	const customContent = (
		<Button
			disabled
			className="pointer-events-none bg-gradient-to-r from-blue-500 to-purple-600 text-white"
		>
			<LoadingSpinner size="md" className="border-white border-t-transparent" />
			<span className="ml-2">Custom Loading</span>
		</Button>
	);

	const customLoading = useCustomFloatingLoading('custom-loading', customContent, {
		priority: 'critical',
	});

	const simulateGlobalLoading = () => {
		setLoading(true);
		setTimeout(() => setLoading(false), 3000);
	};

	const simulateScopedLoading = () => {
		scopedLoading.setLoading(true);
		setTimeout(() => scopedLoading.setLoading(false), 2000);
	};

	const toggleCustomLoading = () => {
		if (customVisible) {
			customLoading.hide();
			setCustomVisible(false);
		} else {
			customLoading.show();
			setCustomVisible(true);
		}
	};

	return (
		<div className="container mx-auto p-6 space-y-6">
			<div className="text-center">
				<h1 className="text-3xl font-bold mb-2">Floating Loading System Test</h1>
				<p className="text-muted-foreground">
					Test the new floating loading components using the floating UI system
				</p>
			</div>

			<div className="grid grid-cols-1 md:grid-cols-3 gap-6">
				{/* Global Loading Test */}
				<Card>
					<CardHeader>
						<CardTitle>Global Loading</CardTitle>
					</CardHeader>
					<CardContent className="space-y-4">
						<p className="text-sm text-muted-foreground">
							Tests the global loading state using the floating UI system.
						</p>
						<div className="space-y-2">
							<Button onClick={simulateGlobalLoading} className="w-full">
								Start Global Loading (3s)
							</Button>
							<div className="text-xs text-muted-foreground">
								Status: {globalLoading.isLoading ? 'Loading...' : 'Idle'}
							</div>
						</div>
					</CardContent>
				</Card>

				{/* Scoped Loading Test */}
				<Card>
					<CardHeader>
						<CardTitle>Scoped Loading</CardTitle>
					</CardHeader>
					<CardContent className="space-y-4">
						<p className="text-sm text-muted-foreground">
							Tests scoped loading for specific operations.
						</p>
						<div className="space-y-2">
							<Button
								onClick={simulateScopedLoading}
								className="w-full"
								variant="outline"
							>
								Start Scoped Loading (2s)
							</Button>
							<div className="text-xs text-muted-foreground">
								Status: {scopedLoading.isLoading ? 'Processing...' : 'Ready'}
							</div>
						</div>
					</CardContent>
				</Card>

				{/* Custom Loading Test */}
				<Card>
					<CardHeader>
						<CardTitle>Custom Loading</CardTitle>
					</CardHeader>
					<CardContent className="space-y-4">
						<p className="text-sm text-muted-foreground">
							Tests custom loading content with gradient styling.
						</p>
						<div className="space-y-2">
							<Button
								onClick={toggleCustomLoading}
								className="w-full"
								variant="secondary"
							>
								{customVisible ? 'Hide' : 'Show'} Custom Loading
							</Button>
							<div className="text-xs text-muted-foreground">
								Status: {customVisible ? 'Visible' : 'Hidden'}
							</div>
						</div>
					</CardContent>
				</Card>
			</div>

			{/* Position Tests */}
			<Card>
				<CardHeader>
					<CardTitle>Position Tests</CardTitle>
				</CardHeader>
				<CardContent>
					<div className="grid grid-cols-2 md:grid-cols-5 gap-4">
						<Button
							variant="outline"
							size="sm"
							onClick={() => {
								globalLoading.show();
								setTimeout(() => globalLoading.hide(), 2000);
							}}
						>
							Test Global Position
						</Button>
						<Button
							variant="outline"
							size="sm"
							onClick={() => {
								scopedLoading.show();
								setTimeout(() => scopedLoading.hide(), 2000);
							}}
						>
							Test Scoped Position
						</Button>
						<Button variant="outline" size="sm" onClick={toggleCustomLoading}>
							Test Custom Position
						</Button>
					</div>
				</CardContent>
			</Card>

			{/* Instructions */}
			<Card>
				<CardHeader>
					<CardTitle>How to Use</CardTitle>
				</CardHeader>
				<CardContent className="space-y-4">
					<div className="space-y-2">
						<h4 className="font-semibold">Global Loading:</h4>
						<code className="block p-2 bg-muted rounded text-sm">
							{`const { setLoading } = useLoading();
setLoading(true); // Shows floating loading
setLoading(false); // Hides floating loading`}
						</code>
					</div>
					<div className="space-y-2">
						<h4 className="font-semibold">Scoped Loading:</h4>
						<code className="block p-2 bg-muted rounded text-sm">
							{`const loading = useFloatingScopedLoading('scope', 'key', options);
loading.setLoading(true); // Shows scoped loading`}
						</code>
					</div>
					<div className="space-y-2">
						<h4 className="font-semibold">Custom Loading:</h4>
						<code className="block p-2 bg-muted rounded text-sm">
							{`const loading = useCustomFloatingLoading('id', content, options);
loading.show(); // Shows custom loading`}
						</code>
					</div>
				</CardContent>
			</Card>
		</div>
	);
}
