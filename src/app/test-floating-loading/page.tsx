'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { useFloatingScopedLoading, useCustomFloatingLoading } from '@/components/floating-ui';
import { useLoading } from '@/contexts/loading-context';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { useState } from 'react';
import {
	Loader2,
	Settings,
	MessageSquare,
	HelpCircle,
	Zap,
	Target,
	Palette,
	Info,
	CheckCircle,
	XCircle,
} from 'lucide-react';

export default function TestFloatingLoadingPage() {
	const { setLoading, isLoading } = useLoading();
	const [testResults, setTestResults] = useState<Record<string, 'success' | 'error' | null>>({});

	// Multiple scoped loading examples
	const collectionsLoading = useFloatingScopedLoading('collections', 'fetch', {
		size: 'md',
	});

	const wordsLoading = useFloatingScopedLoading('words', 'create', {
		size: 'sm',
	});

	const llmLoading = useFloatingScopedLoading('llm', 'generate', {
		size: 'lg',
	});

	// Custom floating loading examples
	const customSpinnerContent = (
		<Button
			disabled
			className="pointer-events-none bg-gradient-to-r from-blue-500 to-purple-600 text-white"
		>
			<LoadingSpinner size="sm" className="border-white border-t-transparent" />
			<span className="ml-2">AI Processing</span>
		</Button>
	);

	const customIconContent = (
		<Button disabled className="pointer-events-none bg-green-600 text-white">
			<Loader2 className="h-4 w-4 animate-spin" />
			<span className="ml-2">Saving...</span>
		</Button>
	);

	const customLoading1 = useCustomFloatingLoading('custom-ai', customSpinnerContent, {
		priority: 'high',
	});

	const customLoading2 = useCustomFloatingLoading('custom-save', customIconContent, {
		priority: 'medium',
	});

	// Test functions
	const simulateGlobalLoading = () => {
		setLoading(true);
		setTestResults((prev) => ({ ...prev, global: null }));
		setTimeout(() => {
			setLoading(false);
			setTestResults((prev) => ({ ...prev, global: 'success' }));
		}, 3000);
	};

	const simulateCollectionsLoading = () => {
		collectionsLoading.setLoading(true);
		setTestResults((prev) => ({ ...prev, collections: null }));
		setTimeout(() => {
			collectionsLoading.setLoading(false);
			setTestResults((prev) => ({ ...prev, collections: 'success' }));
		}, 2000);
	};

	const simulateWordsLoading = () => {
		wordsLoading.setLoading(true);
		setTestResults((prev) => ({ ...prev, words: null }));
		setTimeout(() => {
			wordsLoading.setLoading(false);
			setTestResults((prev) => ({ ...prev, words: 'success' }));
		}, 1500);
	};

	const simulateLLMLoading = () => {
		llmLoading.setLoading(true);
		setTestResults((prev) => ({ ...prev, llm: null }));
		setTimeout(() => {
			llmLoading.setLoading(false);
			setTestResults((prev) => ({ ...prev, llm: 'success' }));
		}, 4000);
	};

	const simulateCustomLoading1 = () => {
		customLoading1.show();
		setTestResults((prev) => ({ ...prev, custom1: null }));
		setTimeout(() => {
			customLoading1.hide();
			setTestResults((prev) => ({ ...prev, custom1: 'success' }));
		}, 2500);
	};

	const simulateCustomLoading2 = () => {
		customLoading2.show();
		setTestResults((prev) => ({ ...prev, custom2: null }));
		setTimeout(() => {
			customLoading2.hide();
			setTestResults((prev) => ({ ...prev, custom2: 'success' }));
		}, 1800);
	};

	const simulateMultipleLoading = () => {
		// Test collision detection and priority system
		setLoading(true);
		collectionsLoading.setLoading(true);
		wordsLoading.setLoading(true);
		customLoading1.show();

		setTestResults((prev) => ({
			...prev,
			global: null,
			collections: null,
			words: null,
			custom1: null,
		}));

		setTimeout(() => {
			setLoading(false);
			setTestResults((prev) => ({ ...prev, global: 'success' }));
		}, 2000);

		setTimeout(() => {
			collectionsLoading.setLoading(false);
			setTestResults((prev) => ({ ...prev, collections: 'success' }));
		}, 3000);

		setTimeout(() => {
			wordsLoading.setLoading(false);
			setTestResults((prev) => ({ ...prev, words: 'success' }));
		}, 3500);

		setTimeout(() => {
			customLoading1.hide();
			setTestResults((prev) => ({ ...prev, custom1: 'success' }));
		}, 4000);
	};

	const getStatusIcon = (status: 'success' | 'error' | null) => {
		if (status === 'success') return <CheckCircle className="h-4 w-4 text-green-500" />;
		if (status === 'error') return <XCircle className="h-4 w-4 text-red-500" />;
		return null;
	};

	return (
		<div className="container mx-auto p-6 space-y-8">
			{/* Header */}
			<div className="text-center space-y-4">
				<h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
					Floating Loading System Test
				</h1>
				<p className="text-lg text-muted-foreground max-w-2xl mx-auto">
					Test the floating loading system using the existing floating notification
					infrastructure. Same system as settings, feedback, and guidance components.
				</p>
				<div className="flex items-center justify-center gap-4 text-sm">
					<Badge variant="outline" className="flex items-center gap-1">
						<Settings className="h-3 w-3" />
						Settings Pattern
					</Badge>
					<Badge variant="outline" className="flex items-center gap-1">
						<MessageSquare className="h-3 w-3" />
						Feedback Pattern
					</Badge>
					<Badge variant="outline" className="flex items-center gap-1">
						<HelpCircle className="h-3 w-3" />
						Guidance Pattern
					</Badge>
				</div>
			</div>

			{/* Global Loading Section */}
			<Card className="border-2 border-blue-200 dark:border-blue-800">
				<CardHeader>
					<CardTitle className="flex items-center gap-2">
						<Zap className="h-5 w-5 text-blue-500" />
						Global Loading
						<Badge variant="secondary">Priority: Critical</Badge>
					</CardTitle>
				</CardHeader>
				<CardContent className="space-y-4">
					<p className="text-sm text-muted-foreground">
						Tests the global loading state using the floating notification system. Uses
						critical priority to always stay on top.
					</p>
					<div className="flex items-center gap-4">
						<Button
							onClick={simulateGlobalLoading}
							disabled={isLoading}
							className="flex-1"
						>
							{isLoading ? (
								<>
									<LoadingSpinner size="sm" className="mr-2" />
									Loading... (3s)
								</>
							) : (
								'Start Global Loading'
							)}
						</Button>
						<div className="flex items-center gap-2">
							{getStatusIcon(testResults.global)}
							<span className="text-sm text-muted-foreground">
								Status: {isLoading ? 'Active' : 'Idle'}
							</span>
						</div>
					</div>
				</CardContent>
			</Card>

			{/* Scoped Loading Section */}
			<Card>
				<CardHeader>
					<CardTitle className="flex items-center gap-2">
						<Target className="h-5 w-5 text-green-500" />
						Scoped Loading Examples
						<Badge variant="outline">Priority: Medium</Badge>
					</CardTitle>
				</CardHeader>
				<CardContent>
					<div className="grid grid-cols-1 md:grid-cols-3 gap-4">
						{/* Collections Loading */}
						<div className="space-y-3">
							<h4 className="font-medium flex items-center gap-2">
								Collections
								<Badge variant="secondary" className="text-xs">
									MD
								</Badge>
							</h4>
							<Button
								onClick={simulateCollectionsLoading}
								disabled={collectionsLoading.isLoading}
								variant="outline"
								className="w-full"
							>
								{collectionsLoading.isLoading ? (
									<>
										<LoadingSpinner size="sm" className="mr-2" />
										Loading...
									</>
								) : (
									'Fetch Collections (2s)'
								)}
							</Button>
							<div className="flex items-center gap-2 text-xs text-muted-foreground">
								{getStatusIcon(testResults.collections)}
								Status: {collectionsLoading.isLoading ? 'Loading' : 'Ready'}
							</div>
						</div>

						{/* Words Loading */}
						<div className="space-y-3">
							<h4 className="font-medium flex items-center gap-2">
								Words
								<Badge variant="secondary" className="text-xs">
									SM
								</Badge>
							</h4>
							<Button
								onClick={simulateWordsLoading}
								disabled={wordsLoading.isLoading}
								variant="outline"
								className="w-full"
							>
								{wordsLoading.isLoading ? (
									<>
										<LoadingSpinner size="sm" className="mr-2" />
										Creating...
									</>
								) : (
									'Create Word (1.5s)'
								)}
							</Button>
							<div className="flex items-center gap-2 text-xs text-muted-foreground">
								{getStatusIcon(testResults.words)}
								Status: {wordsLoading.isLoading ? 'Creating' : 'Ready'}
							</div>
						</div>

						{/* LLM Loading */}
						<div className="space-y-3">
							<h4 className="font-medium flex items-center gap-2">
								LLM Generate
								<Badge variant="secondary" className="text-xs">
									LG
								</Badge>
							</h4>
							<Button
								onClick={simulateLLMLoading}
								disabled={llmLoading.isLoading}
								variant="outline"
								className="w-full"
							>
								{llmLoading.isLoading ? (
									<>
										<LoadingSpinner size="sm" className="mr-2" />
										Generating...
									</>
								) : (
									'Generate Content (4s)'
								)}
							</Button>
							<div className="flex items-center gap-2 text-xs text-muted-foreground">
								{getStatusIcon(testResults.llm)}
								Status: {llmLoading.isLoading ? 'Generating' : 'Ready'}
							</div>
						</div>
					</div>
				</CardContent>
			</Card>

			{/* Custom Loading Section */}
			<Card>
				<CardHeader>
					<CardTitle className="flex items-center gap-2">
						<Palette className="h-5 w-5 text-purple-500" />
						Custom Loading Examples
						<Badge variant="outline">Custom Content</Badge>
					</CardTitle>
				</CardHeader>
				<CardContent>
					<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
						{/* Custom AI Loading */}
						<div className="space-y-3">
							<h4 className="font-medium">AI Processing</h4>
							<Button
								onClick={simulateCustomLoading1}
								disabled={customLoading1.isVisible}
								variant="secondary"
								className="w-full"
							>
								{customLoading1.isVisible ? (
									<>
										<LoadingSpinner size="sm" className="mr-2" />
										Processing...
									</>
								) : (
									'Start AI Processing (2.5s)'
								)}
							</Button>
							<div className="flex items-center gap-2 text-xs text-muted-foreground">
								{getStatusIcon(testResults.custom1)}
								Status: {customLoading1.isVisible ? 'Processing' : 'Ready'}
							</div>
						</div>

						{/* Custom Save Loading */}
						<div className="space-y-3">
							<h4 className="font-medium">Save Operation</h4>
							<Button
								onClick={simulateCustomLoading2}
								disabled={customLoading2.isVisible}
								variant="secondary"
								className="w-full"
							>
								{customLoading2.isVisible ? (
									<>
										<Loader2 className="h-4 w-4 animate-spin mr-2" />
										Saving...
									</>
								) : (
									'Start Save (1.8s)'
								)}
							</Button>
							<div className="flex items-center gap-2 text-xs text-muted-foreground">
								{getStatusIcon(testResults.custom2)}
								Status: {customLoading2.isVisible ? 'Saving' : 'Ready'}
							</div>
						</div>
					</div>
				</CardContent>
			</Card>

			{/* Multiple Loading Test */}
			<Card className="border-2 border-orange-200 dark:border-orange-800">
				<CardHeader>
					<CardTitle className="flex items-center gap-2">
						<Info className="h-5 w-5 text-orange-500" />
						Priority & Collision Test
						<Badge variant="destructive">Advanced</Badge>
					</CardTitle>
				</CardHeader>
				<CardContent className="space-y-4">
					<p className="text-sm text-muted-foreground">
						Test multiple loading indicators simultaneously to see priority system and
						collision detection in action. Global (critical) should always be visible,
						others may be managed by collision detection.
					</p>
					<Button
						onClick={simulateMultipleLoading}
						variant="destructive"
						className="w-full"
					>
						Start Multiple Loading Test
					</Button>
					<div className="grid grid-cols-2 md:grid-cols-4 gap-2 text-xs">
						<div className="flex items-center gap-1">
							{getStatusIcon(testResults.global)}
							<span>Global</span>
						</div>
						<div className="flex items-center gap-1">
							{getStatusIcon(testResults.collections)}
							<span>Collections</span>
						</div>
						<div className="flex items-center gap-1">
							{getStatusIcon(testResults.words)}
							<span>Words</span>
						</div>
						<div className="flex items-center gap-1">
							{getStatusIcon(testResults.custom1)}
							<span>Custom</span>
						</div>
					</div>
				</CardContent>
			</Card>

			{/* Documentation */}
			<Card>
				<CardHeader>
					<CardTitle>Implementation Guide</CardTitle>
				</CardHeader>
				<CardContent className="space-y-6">
					<div className="grid grid-cols-1 md:grid-cols-3 gap-6">
						{/* Global Loading */}
						<div className="space-y-3">
							<h4 className="font-semibold flex items-center gap-2">
								<Zap className="h-4 w-4 text-blue-500" />
								Global Loading
							</h4>
							<div className="space-y-2">
								<code className="block p-3 bg-muted rounded text-sm">
									{`const { setLoading } = useLoading();

// Show global loading
setLoading(true);

// Hide global loading
setLoading(false);`}
								</code>
								<p className="text-xs text-muted-foreground">
									Uses floating notification system with critical priority. Always
									visible on top.
								</p>
							</div>
						</div>

						{/* Scoped Loading */}
						<div className="space-y-3">
							<h4 className="font-semibold flex items-center gap-2">
								<Target className="h-4 w-4 text-green-500" />
								Scoped Loading
							</h4>
							<div className="space-y-2">
								<code className="block p-3 bg-muted rounded text-sm">
									{`const loading = useFloatingScopedLoading(
  'scope', 'key', { size: 'md' }
);

loading.setLoading(true);
loading.setLoading(false);`}
								</code>
								<p className="text-xs text-muted-foreground">
									Uses floating notification system with medium priority. Subject
									to collision detection.
								</p>
							</div>
						</div>

						{/* Custom Loading */}
						<div className="space-y-3">
							<h4 className="font-semibold flex items-center gap-2">
								<Palette className="h-4 w-4 text-purple-500" />
								Custom Loading
							</h4>
							<div className="space-y-2">
								<code className="block p-3 bg-muted rounded text-sm">
									{`const content = <Button disabled>
  <LoadingSpinner />
  Custom
</Button>;

const loading = useCustomFloatingLoading(
  'id', content, { priority: 'high' }
);

loading.show();
loading.hide();`}
								</code>
								<p className="text-xs text-muted-foreground">
									Uses floating notification system with custom content and
									configurable priority.
								</p>
							</div>
						</div>
					</div>

					<Separator />

					<div className="space-y-3">
						<h4 className="font-semibold">Key Features</h4>
						<div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
							<div className="space-y-2">
								<h5 className="font-medium">✅ Consistent System</h5>
								<p className="text-muted-foreground">
									Uses the same floating notification system as settings,
									feedback, and guidance components.
								</p>
							</div>
							<div className="space-y-2">
								<h5 className="font-medium">✅ Priority Management</h5>
								<p className="text-muted-foreground">
									Critical priority for global loading ensures it's always visible
									above other elements.
								</p>
							</div>
							<div className="space-y-2">
								<h5 className="font-medium">✅ Collision Detection</h5>
								<p className="text-muted-foreground">
									Automatic collision detection prevents overlapping notifications
									(except critical priority).
								</p>
							</div>
							<div className="space-y-2">
								<h5 className="font-medium">✅ Simple API</h5>
								<p className="text-muted-foreground">
									Clean, simple API that integrates seamlessly with existing
									loading context.
								</p>
							</div>
						</div>
					</div>
				</CardContent>
			</Card>
		</div>
	);
}
