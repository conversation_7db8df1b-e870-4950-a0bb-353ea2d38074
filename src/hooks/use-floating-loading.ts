'use client';

import { useFloatingNotification } from '@/hooks/use-floating-ui';
import { useAllLoadingStates } from '@/contexts/loading-context';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { Button } from '@/components/ui/button';
import { useEffect } from 'react';
import React from 'react';

// ============================================================================
// TYPES
// ============================================================================

interface FloatingLoadingOptions {
	id?: string;
	size?: 'sm' | 'md' | 'lg';
	className?: string;
	priority?: 'low' | 'medium' | 'high' | 'critical';
	persistent?: boolean;
}

// ============================================================================
// GLOBAL FLOATING LOADING HOOK
// ============================================================================

export function useFloatingLoading(options: FloatingLoadingOptions = {}) {
	const { isLoading, loadingStates } = useAllLoadingStates();
	const {
		id = 'global-floating-loading',
		size = 'md',
		className = '',
		priority = 'critical',
		persistent = true,
	} = options;

	// Check if any loading state is active (global or any scoped)
	const hasAnyLoading = isLoading || Object.values(loadingStates).some((state) => state);

	// Create simple disabled spinner button
	const loadingContent = React.createElement(
		Button,
		{
			disabled: true,
			className: `pointer-events-none ${className}`,
		},
		React.createElement(LoadingSpinner, { size })
	);

	const { show, hide, isVisible } = useFloatingNotification(id, loadingContent, {
		priority,
		animation: { type: 'scale', duration: 200 },
		collisionDetection: priority === 'critical' ? false : true,
		persistent,
	});

	// Show/hide based on ANY loading state
	useEffect(() => {
		if (hasAnyLoading) {
			show();
		} else {
			hide();
		}
	}, [hasAnyLoading, show, hide]);

	return {
		hasAnyLoading,
		show,
		hide,
		isVisible,
	};
}
