'use client';

import { useAllLoadingStates } from '@/contexts/loading-context';

// ============================================================================
// TYPES
// ============================================================================

interface FloatingLoadingOptions {
	id?: string;
	className?: string;
	priority?: 'low' | 'medium' | 'high' | 'critical';
	persistent?: boolean;
}

// ============================================================================
// GLOBAL FLOATING LOADING HOOK
// ============================================================================

export function useFloatingLoading(_options: FloatingLoadingOptions = {}) {
	const { isLoading, loadingStates } = useAllLoadingStates();

	// Check if any loading state is active (global or any scoped)
	const hasAnyLoading = isLoading || Object.values(loadingStates).some((state) => state);

	// This hook is now deprecated - loading is handled in SimpleEnhancedFloatingButtons
	// Kept for backward compatibility
	return {
		hasAnyLoading,
		show: () => {}, // No-op
		hide: () => {}, // No-op
		isVisible: hasAnyLoading,
	};
}
