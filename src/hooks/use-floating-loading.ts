'use client';

import { useFloatingNotification } from '@/hooks/use-floating-ui';
import { useLoading, useScopedLoading } from '@/contexts/loading-context';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { Button } from '@/components/ui/button';
import { useCallback, useEffect } from 'react';
import React from 'react';

// ============================================================================
// TYPES
// ============================================================================

interface FloatingLoadingOptions {
	id?: string;
	size?: 'sm' | 'md' | 'lg';
	className?: string;
	priority?: 'low' | 'medium' | 'high' | 'critical';
	persistent?: boolean;
}

// ============================================================================
// GLOBAL FLOATING LOADING HOOK
// ============================================================================

export function useFloatingLoading(options: FloatingLoadingOptions = {}) {
	const { isLoading, setLoading } = useLoading();
	const {
		id = 'global-floating-loading',
		size = 'md',
		className = '',
		priority = 'critical',
		persistent = true,
	} = options;

	// Create simple disabled spinner button
	const loadingContent = React.createElement(
		Button,
		{
			disabled: true,
			className: `pointer-events-none ${className}`,
		},
		React.createElement(LoadingSpinner, { size })
	);

	const { show, hide, isVisible } = useFloatingNotification(id, loadingContent, {
		priority,
		animation: { type: 'scale', duration: 200 },
		collisionDetection: priority === 'critical' ? false : true,
		persistent,
	});

	// Show/hide based on loading state
	useEffect(() => {
		if (isLoading) {
			show();
		} else {
			hide();
		}
	}, [isLoading, show, hide]);

	return {
		isLoading,
		setLoading,
		show,
		hide,
		isVisible,
	};
}

// ============================================================================
// SCOPED FLOATING LOADING HOOK
// ============================================================================

export function useFloatingScopedLoading(
	scope: string,
	loadingKey: string,
	options: FloatingLoadingOptions = {}
) {
	const { getLoading, setLoading: setScopedLoading } = useScopedLoading(scope);
	const isLoading = getLoading(loadingKey);

	const {
		id = `scoped-floating-loading-${scope}-${loadingKey}`,
		size = 'md',
		className = '',
		priority = 'medium',
		persistent = false,
	} = options;

	// Create simple disabled spinner button
	const loadingContent = React.createElement(
		Button,
		{
			disabled: true,
			className: `pointer-events-none ${className}`,
		},
		React.createElement(LoadingSpinner, { size })
	);

	const { show, hide, isVisible } = useFloatingNotification(id, loadingContent, {
		priority,
		animation: { type: 'slide', duration: 250 },
		collisionDetection: true,
		persistent,
	});

	useEffect(() => {
		if (isLoading) {
			show();
		} else {
			hide();
		}
	}, [isLoading, show, hide]);

	const setLoading = useCallback(
		(loading: boolean) => {
			setScopedLoading(loadingKey, loading);
		},
		[loadingKey, setScopedLoading]
	);

	return {
		isLoading,
		setLoading,
		show,
		hide,
		isVisible,
	};
}

// ============================================================================
// CUSTOM FLOATING LOADING HOOK
// ============================================================================

export function useCustomFloatingLoading(
	id: string,
	content: React.ReactNode,
	options: Omit<FloatingLoadingOptions, 'size'> = {}
) {
	const { className = '', priority = 'medium', persistent = false } = options;

	const { show, hide, isVisible, toggle } = useFloatingNotification(id, content, {
		priority,
		animation: { type: 'scale', duration: 200 },
		collisionDetection: true,
		persistent,
		className,
	});

	return {
		show,
		hide,
		toggle,
		isVisible,
	};
}
