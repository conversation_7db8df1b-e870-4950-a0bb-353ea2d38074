'use client';

import { useFloatingNotification } from '@/hooks/use-floating-ui';
import { useAllLoadingStates } from '@/contexts/loading-context';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { Button } from '@/components/ui/button';
import { useEffect } from 'react';
import React from 'react';

// ============================================================================
// TYPES
// ============================================================================

interface FloatingLoadingOptions {
	id?: string;
	className?: string;
	priority?: 'low' | 'medium' | 'high' | 'critical';
	persistent?: boolean;
}

// ============================================================================
// GLOBAL FLOATING LOADING HOOK
// ============================================================================

export function useFloatingLoading(options: FloatingLoadingOptions = {}) {
	const { isLoading, loadingStates } = useAllLoadingStates();
	const {
		id = 'global-floating-loading',
		className = '',
		priority = 'critical',
		persistent = true,
	} = options;

	// Check if any loading state is active (global or any scoped)
	const hasAnyLoading = isLoading || Object.values(loadingStates).some((state) => state);

	// Create loading button with same style as feedback/settings buttons
	const loadingContent = React.createElement(
		Button,
		{
			disabled: true,
			size: 'icon',
			className: `h-14 w-14 rounded-full shadow-lg pointer-events-none opacity-70 ${className}`,
		},
		React.createElement(LoadingSpinner, { size: 'md' })
	);

	const { show, hide, isVisible } = useFloatingNotification(id, loadingContent, {
		priority,
		animation: { type: 'scale', duration: 200 },
		collisionDetection: priority === 'critical' ? false : true,
		persistent,
	});

	// Show/hide based on ANY loading state
	useEffect(() => {
		if (hasAnyLoading) {
			show();
		} else {
			hide();
		}
	}, [hasAnyLoading, show, hide]);

	return {
		hasAnyLoading,
		show,
		hide,
		isVisible,
	};
}
