'use client';

import { useFloatingUIElement } from '@/hooks/use-floating-ui';
import { useLoading, useScopedLoading } from '@/contexts/loading-context';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { useCallback, useEffect } from 'react';
import React from 'react';

// ============================================================================
// TYPES
// ============================================================================

interface FloatingLoadingOptions {
	id?: string;
	size?: 'sm' | 'md' | 'lg';
	message?: string;
	position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left' | 'center';
	className?: string;
	priority?: 'low' | 'medium' | 'high' | 'critical';
	persistent?: boolean;
}

// ============================================================================
// GLOBAL FLOATING LOADING HOOK
// ============================================================================

export function useFloatingLoading(options: FloatingLoadingOptions = {}) {
	const { isLoading, setLoading } = useLoading();
	const {
		id = 'global-floating-loading',
		size = 'md',
		message = 'Loading...',
		position = 'bottom-right',
		className = '',
		priority = 'high',
		persistent = true,
	} = options;

	// Create loading content
	const loadingContent = React.createElement(
		'div',
		{
			className: `flex items-center gap-3 p-3 rounded-lg bg-white dark:bg-gray-800 shadow-lg border border-gray-200 dark:border-gray-700 ${className}`,
		},
		React.createElement(LoadingSpinner, { size }),
		message &&
			React.createElement(
				'span',
				{
					className: 'text-sm font-medium text-gray-700 dark:text-gray-300',
				},
				message
			)
	);

	// Position coordinates based on position prop
	const getCoordinates = useCallback(() => {
		switch (position) {
			case 'top-right':
				return { top: 16, right: 16 };
			case 'top-left':
				return { top: 16, left: 16 };
			case 'bottom-left':
				return { bottom: 16, left: 16 };
			case 'center':
				return { top: 50, left: 50 };
			case 'bottom-right':
			default:
				return { bottom: 16, right: 16 };
		}
	}, [position]);

	const { show, hide, isVisible } = useFloatingUIElement(id, loadingContent, {
		type: 'notification',
		priority,
		position: position === 'center' ? 'center' : 'custom',
		coordinates: getCoordinates(),
		style: position === 'center' ? { transform: 'translate(-50%, -50%)' } : undefined,
		animation: { type: 'scale', duration: 200 },
		collisionDetection: true,
		persistent,
	});

	// Show/hide based on loading state
	useEffect(() => {
		if (isLoading) {
			show();
		} else {
			hide();
		}
	}, [isLoading, show, hide]);

	return {
		isLoading,
		setLoading,
		show,
		hide,
		isVisible,
	};
}

// ============================================================================
// SCOPED FLOATING LOADING HOOK
// ============================================================================

export function useFloatingScopedLoading(
	scope: string,
	loadingKey: string,
	options: FloatingLoadingOptions = {}
) {
	const { getLoading, setLoading: setScopedLoading } = useScopedLoading(scope);
	const isLoading = getLoading(loadingKey);

	const {
		id = `scoped-floating-loading-${scope}-${loadingKey}`,
		size = 'md',
		message,
		position = 'bottom-right',
		className = '',
		priority = 'medium',
		persistent = false,
	} = options;

	// Create loading content
	const loadingContent = React.createElement(
		'div',
		{
			className: `flex items-center gap-3 p-3 rounded-lg bg-white dark:bg-gray-800 shadow-lg border border-gray-200 dark:border-gray-700 ${className}`,
		},
		React.createElement(LoadingSpinner, { size }),
		message &&
			React.createElement(
				'span',
				{
					className: 'text-sm font-medium text-gray-700 dark:text-gray-300',
				},
				message
			)
	);

	const getCoordinates = useCallback(() => {
		switch (position) {
			case 'top-right':
				return { top: 16, right: 16 };
			case 'top-left':
				return { top: 16, left: 16 };
			case 'bottom-left':
				return { bottom: 16, left: 16 };
			case 'center':
				return { top: 50, left: 50 };
			case 'bottom-right':
			default:
				return { bottom: 16, right: 16 };
		}
	}, [position]);

	const { show, hide, isVisible } = useFloatingUIElement(id, loadingContent, {
		type: 'notification',
		priority,
		position: position === 'center' ? 'center' : 'custom',
		coordinates: getCoordinates(),
		style: position === 'center' ? { transform: 'translate(-50%, -50%)' } : undefined,
		animation: { type: 'slide', duration: 250 },
		collisionDetection: true,
		persistent,
	});

	useEffect(() => {
		if (isLoading) {
			show();
		} else {
			hide();
		}
	}, [isLoading, show, hide]);

	const setLoading = useCallback(
		(loading: boolean) => {
			setScopedLoading(loadingKey, loading);
		},
		[loadingKey, setScopedLoading]
	);

	return {
		isLoading,
		setLoading,
		show,
		hide,
		isVisible,
	};
}

// ============================================================================
// CUSTOM FLOATING LOADING HOOK
// ============================================================================

export function useCustomFloatingLoading(
	id: string,
	content: React.ReactNode,
	options: Omit<FloatingLoadingOptions, 'message' | 'size'> = {}
) {
	const {
		position = 'bottom-right',
		className = '',
		priority = 'medium',
		persistent = false,
	} = options;

	const getCoordinates = useCallback(() => {
		switch (position) {
			case 'top-right':
				return { top: 16, right: 16 };
			case 'top-left':
				return { top: 16, left: 16 };
			case 'bottom-left':
				return { bottom: 16, left: 16 };
			case 'center':
				return { top: 50, left: 50 };
			case 'bottom-right':
			default:
				return { bottom: 16, right: 16 };
		}
	}, [position]);

	const { show, hide, isVisible, toggle } = useFloatingUIElement(id, content, {
		type: 'notification',
		priority,
		position: position === 'center' ? 'center' : 'custom',
		coordinates: getCoordinates(),
		style: position === 'center' ? { transform: 'translate(-50%, -50%)' } : undefined,
		animation: { type: 'scale', duration: 200 },
		collisionDetection: true,
		persistent,
		className,
	});

	return {
		show,
		hide,
		toggle,
		isVisible,
	};
}
