'use client';

import { useState } from 'react';
import { <PERSON><PERSON> } from './button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from './card';
import { LoadingSpinner } from './loading-spinner';
import { ListSkeleton } from './list-skeleton';
import { HomeSkeleton } from './home-skeleton';
import { CollectionsSkeleton } from './collections-skeleton';
import { PageSkeleton, FullPageLoading } from './page-skeleton';
import { StatsSkeleton } from './stats-skeleton';
import { PracticeSessionSkeleton } from '../../app/collections/[id]/components/practice-session-skeleton';
import { useFloatingLoading, useFloatingScopedLoading } from '@/components/floating-ui';
import { useLoading } from '@/contexts/loading-context';

/**
 * Loading Test Component
 * This component is useful for testing and demonstrating all loading states
 * It should only be used in development/testing environments
 */
export function LoadingTest() {
	const [activeDemo, setActiveDemo] = useState<string | null>(null);
	const { setLoading } = useLoading();

	// Floating loading examples
	const globalFloating = useFloatingLoading({
		size: 'md',
	});

	const scopedFloating = useFloatingScopedLoading('test', 'demo', {
		size: 'lg',
	});

	const demos = [
		{ id: 'spinner', name: 'Loading Spinner', component: <LoadingSpinner size="lg" /> },
		{ id: 'list', name: 'List Skeleton', component: <ListSkeleton /> },
		{ id: 'home', name: 'Home Skeleton', component: <HomeSkeleton /> },
		{ id: 'collections', name: 'Collections Skeleton', component: <CollectionsSkeleton /> },
		{ id: 'stats', name: 'Stats Skeleton', component: <StatsSkeleton /> },
		{ id: 'page-default', name: 'Page Skeleton (Default)', component: <PageSkeleton /> },
		{
			id: 'page-dashboard',
			name: 'Page Skeleton (Dashboard)',
			component: <PageSkeleton type="dashboard" />,
		},
		{ id: 'page-form', name: 'Page Skeleton (Form)', component: <PageSkeleton type="form" /> },
		{ id: 'page-list', name: 'Page Skeleton (List)', component: <PageSkeleton type="list" /> },
		{
			id: 'page-detail',
			name: 'Page Skeleton (Detail)',
			component: <PageSkeleton type="detail" />,
		},
		{
			id: 'full-page',
			name: 'Full Page Loading',
			component: <FullPageLoading message="Loading application..." />,
		},
		{
			id: 'practice-mcq',
			name: 'Practice Skeleton (MCQ)',
			component: <PracticeSessionSkeleton type="mcq" />,
		},
		{
			id: 'practice-review',
			name: 'Practice Skeleton (Review)',
			component: <PracticeSessionSkeleton type="review" />,
		},
		{
			id: 'practice-paragraph',
			name: 'Practice Skeleton (Paragraph)',
			component: <PracticeSessionSkeleton type="paragraph" />,
		},
		{
			id: 'practice-grammar',
			name: 'Practice Skeleton (Grammar)',
			component: <PracticeSessionSkeleton type="grammar" />,
		},
	];

	const floatingDemos = [
		{
			id: 'global-floating',
			name: 'Global Floating Loading',
			action: () => {
				setLoading(true);
				setTimeout(() => setLoading(false), 3000);
			},
		},
		{
			id: 'scoped-floating',
			name: 'Scoped Floating Loading',
			action: () => {
				scopedFloating.setLoading(true);
				setTimeout(() => scopedFloating.setLoading(false), 2000);
			},
		},
	];

	return (
		<div className="p-6 space-y-6">
			<Card>
				<CardHeader>
					<CardTitle>Loading States Demo</CardTitle>
				</CardHeader>
				<CardContent>
					<div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
						{demos.map((demo) => (
							<Button
								key={demo.id}
								variant={activeDemo === demo.id ? 'default' : 'outline'}
								size="sm"
								onClick={() =>
									setActiveDemo(activeDemo === demo.id ? null : demo.id)
								}
								className="text-xs"
							>
								{demo.name}
							</Button>
						))}
					</div>
				</CardContent>
			</Card>

			<Card>
				<CardHeader>
					<CardTitle>Floating Loading System</CardTitle>
				</CardHeader>
				<CardContent>
					<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
						{floatingDemos.map((demo) => (
							<Button
								key={demo.id}
								variant="secondary"
								onClick={demo.action}
								className="h-auto py-3"
							>
								{demo.name}
							</Button>
						))}
					</div>
					<div className="mt-4 p-4 bg-muted rounded-lg">
						<p className="text-sm text-muted-foreground">
							Click the buttons above to test the new floating loading system. Loading
							indicators will appear as floating elements that don't affect page
							layout.
						</p>
					</div>
				</CardContent>
			</Card>

			{activeDemo && (
				<Card>
					<CardHeader>
						<CardTitle>{demos.find((d) => d.id === activeDemo)?.name}</CardTitle>
					</CardHeader>
					<CardContent>
						<div className="border rounded-lg p-4 bg-background">
							{demos.find((d) => d.id === activeDemo)?.component}
						</div>
					</CardContent>
				</Card>
			)}

			{!activeDemo && (
				<Card>
					<CardContent className="pt-6">
						<div className="text-center text-muted-foreground">
							Select a loading state above to preview it
						</div>
					</CardContent>
				</Card>
			)}
		</div>
	);
}

/**
 * Simple loading state tester for individual components
 */
export function useLoadingTest(initialState = false) {
	const [isLoading, setIsLoading] = useState(initialState);

	const startLoading = () => setIsLoading(true);
	const stopLoading = () => setIsLoading(false);
	const toggleLoading = () => setIsLoading(!isLoading);

	const simulateLoading = (duration = 2000) => {
		setIsLoading(true);
		setTimeout(() => setIsLoading(false), duration);
	};

	return {
		isLoading,
		startLoading,
		stopLoading,
		toggleLoading,
		simulateLoading,
	};
}
