// ============================================================================
// FLOATING UI EXPORTS
// ============================================================================

// Components
export {
	FloatingUIManager,
	FloatingUIPortal,
	floatingUIStyles,
	FloatingUIWrapper,
} from './floating-ui-manager';

export { FloatingLoading, FloatingLoadingManager } from './floating-loading';

// Context
export { FloatingUIProvider, useFloatingUI } from '@/contexts/floating-ui-context';

// Hooks
export {
	useFloatingDropdown,
	useFloatingModal,
	useFloatingNotification,
	useFloatingSettings,
	useFloatingTooltip,
	useFloatingUIElement,
	useFloatingUIManager,
	useResponsiveFloatingUI,
} from '@/hooks/use-floating-ui';

export { useFloatingLoading } from '@/hooks/use-floating-loading';

// Types
export type {
	FloatingAnimationConfig,
	FloatingCoordinates,
	FloatingDimensions,
	FloatingElement,
	FloatingPosition,
	FloatingPriority,
	FloatingSettingsOptions,
	FloatingUIActions,
	FloatingUIConfig,
	FloatingUIContextType,
	FloatingUIManagerProps,
	FloatingUIState,
	FloatingUIType,
	UseFloatingUIOptions,
	UseFloatingUIReturn,
} from '@/types/floating-ui';

export { DEFAULT_FLOATING_CONFIG } from '@/types/floating-ui';
