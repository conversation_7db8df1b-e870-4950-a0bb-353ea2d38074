'use client';

import { useFloatingUIElement } from '@/hooks/use-floating-ui';
import { useLoading, useScopedLoading } from '@/contexts/loading-context';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { useEffect } from 'react';

// ============================================================================
// FLOATING LOADING COMPONENT
// ============================================================================

interface FloatingLoadingProps {
	id?: string;
	size?: 'sm' | 'md' | 'lg';
	message?: string;
	position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left' | 'center';
	className?: string;
}

export function FloatingLoading({
	id = 'global-loading',
	size = 'md',
	message,
	position = 'bottom-right',
	className = '',
}: FloatingLoadingProps) {
	const { isLoading } = useLoading();

	// Create loading content
	const loadingContent = (
		<div
			className={`flex items-center gap-3 p-3 rounded-lg bg-white dark:bg-gray-800 shadow-lg border border-gray-200 dark:border-gray-700 ${className}`}
		>
			<LoadingSpinner size={size} />
			{message && (
				<span className="text-sm font-medium text-gray-700 dark:text-gray-300">
					{message}
				</span>
			)}
		</div>
	);

	// Position coordinates based on position prop
	const getCoordinates = () => {
		switch (position) {
			case 'top-right':
				return { top: 16, right: 16 };
			case 'top-left':
				return { top: 16, left: 16 };
			case 'bottom-left':
				return { bottom: 16, left: 16 };
			case 'center':
				return { top: 50, left: 50 };
			case 'bottom-right':
			default:
				return { bottom: 16, right: 16 };
		}
	};

	const { show, hide } = useFloatingUIElement(id, loadingContent, {
		type: 'notification',
		priority: 'high',
		position: position === 'center' ? 'center' : 'custom',
		coordinates: getCoordinates(),
		style: position === 'center' ? { transform: 'translate(-50%, -50%)' } : undefined,
		animation: { type: 'scale', duration: 200 },
		collisionDetection: true,
		persistent: true,
	});

	// Show/hide based on loading state
	useEffect(() => {
		if (isLoading) {
			show();
		} else {
			hide();
		}
	}, [isLoading, show, hide]);

	// This component doesn't render anything directly
	// The floating UI system handles the rendering
	return null;
}

// ============================================================================
// FLOATING SCOPED LOADING COMPONENT
// ============================================================================

interface FloatingScopedLoadingProps extends FloatingLoadingProps {
	scope: string;
	loadingKey: string;
}

export function FloatingScopedLoading({
	scope,
	loadingKey,
	id,
	...props
}: FloatingScopedLoadingProps) {
	const { getLoading } = useScopedLoading(scope);
	const isLoading = getLoading(loadingKey);

	const loadingContent = (
		<div
			className={`flex items-center gap-3 p-3 rounded-lg bg-white dark:bg-gray-800 shadow-lg border border-gray-200 dark:border-gray-700 ${
				props.className || ''
			}`}
		>
			<LoadingSpinner size={props.size || 'md'} />
			{props.message && (
				<span className="text-sm font-medium text-gray-700 dark:text-gray-300">
					{props.message}
				</span>
			)}
		</div>
	);

	const getCoordinates = () => {
		const position = props.position || 'bottom-right';
		switch (position) {
			case 'top-right':
				return { top: 16, right: 16 };
			case 'top-left':
				return { top: 16, left: 16 };
			case 'bottom-left':
				return { bottom: 16, left: 16 };
			case 'center':
				return { top: 50, left: 50 };
			case 'bottom-right':
			default:
				return { bottom: 16, right: 16 };
		}
	};

	const { show, hide } = useFloatingUIElement(
		id || `scoped-loading-${scope}-${loadingKey}`,
		loadingContent,
		{
			type: 'notification',
			priority: 'medium',
			position: props.position === 'center' ? 'center' : 'custom',
			coordinates: getCoordinates(),
			style: props.position === 'center' ? { transform: 'translate(-50%, -50%)' } : undefined,
			animation: { type: 'slide', duration: 250 },
			collisionDetection: true,
		}
	);

	useEffect(() => {
		if (isLoading) {
			show();
		} else {
			hide();
		}
	}, [isLoading, show, hide]);

	return null;
}

// ============================================================================
// FLOATING LOADING MANAGER
// ============================================================================

export function FloatingLoadingManager() {
	return (
		<>
			{/* Global loading indicator */}
			<FloatingLoading id="global-loading" message="Loading..." position="bottom-right" />
		</>
	);
}
