'use client';

import { useFloatingUIElement } from '@/hooks/use-floating-ui';
import { useLoading, useScopedLoading } from '@/contexts/loading-context';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { Button } from '@/components/ui/button';
import { useEffect } from 'react';

// ============================================================================
// FLOATING LOADING COMPONENT
// ============================================================================

interface FloatingLoadingProps {
	id?: string;
	size?: 'sm' | 'md' | 'lg';
	className?: string;
}

export function FloatingLoading({
	id = 'global-loading',
	size = 'md',
	className = '',
}: FloatingLoadingProps) {
	const { isLoading } = useLoading();

	// Create simple disabled spinner button
	const loadingContent = (
		<Button disabled className={`pointer-events-none ${className}`}>
			<LoadingSpinner size={size} />
		</Button>
	);

	const { show, hide } = useFloatingUIElement(id, loadingContent, {
		type: 'notification',
		priority: 'critical',
		position: 'bottom-right',
		animation: { type: 'scale', duration: 200 },
		collisionDetection: false,
		persistent: true,
	});

	// Show/hide based on loading state
	useEffect(() => {
		if (isLoading) {
			show();
		} else {
			hide();
		}
	}, [isLoading, show, hide]);

	// This component doesn't render anything directly
	// The floating UI system handles the rendering
	return null;
}

// ============================================================================
// FLOATING SCOPED LOADING COMPONENT
// ============================================================================

interface FloatingScopedLoadingProps extends FloatingLoadingProps {
	scope: string;
	loadingKey: string;
}

export function FloatingScopedLoading({
	scope,
	loadingKey,
	id,
	...props
}: FloatingScopedLoadingProps) {
	const { getLoading } = useScopedLoading(scope);
	const isLoading = getLoading(loadingKey);

	// Create simple disabled spinner button
	const loadingContent = (
		<Button disabled className={`pointer-events-none ${props.className || ''}`}>
			<LoadingSpinner size={props.size || 'md'} />
		</Button>
	);

	const { show, hide } = useFloatingUIElement(
		id || `scoped-loading-${scope}-${loadingKey}`,
		loadingContent,
		{
			type: 'notification',
			priority: 'medium',
			position: 'bottom-right',
			animation: { type: 'slide', duration: 250 },
			collisionDetection: true,
		}
	);

	useEffect(() => {
		if (isLoading) {
			show();
		} else {
			hide();
		}
	}, [isLoading, show, hide]);

	return null;
}

// ============================================================================
// FLOATING LOADING MANAGER
// ============================================================================

export function FloatingLoadingManager() {
	return (
		<>
			{/* Global loading indicator với priority cao nhất */}
			<FloatingLoading id="global-loading" />
		</>
	);
}
