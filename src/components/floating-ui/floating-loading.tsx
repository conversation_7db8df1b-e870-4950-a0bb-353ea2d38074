'use client';

// ============================================================================
// FLOATING LOADING COMPONENT
// ============================================================================

interface FloatingLoadingProps {
	id?: string;
	className?: string;
}

export function FloatingLoading(_props: FloatingLoadingProps) {
	// This component is now deprecated - loading is handled in SimpleEnhancedFloatingButtons
	// Kept for backward compatibility
	return null;
}

// ============================================================================
// FLOATING LOADING MANAGER (DEPRECATED)
// ============================================================================
// Note: Global loading is now managed in SimpleEnhancedFloatingButtons
// This component is kept for backward compatibility but not used

export function FloatingLoadingManager() {
	return null; // No longer needed - loading is handled in SimpleEnhancedFloatingButtons
}
