'use client';

import { useFloatingNotification } from '@/hooks/use-floating-ui';
import { useLoading, useScopedLoading } from '@/contexts/loading-context';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { Button } from '@/components/ui/button';
import { useEffect } from 'react';

// ============================================================================
// FLOATING LOADING COMPONENT
// ============================================================================

interface FloatingLoadingProps {
	id?: string;
	size?: 'sm' | 'md' | 'lg';
	className?: string;
}

export function FloatingLoading({
	id = 'global-loading',
	size = 'md',
	className = '',
}: FloatingLoadingProps) {
	const { isLoading } = useLoading();

	// Create simple disabled spinner button
	const loadingContent = (
		<Button disabled className={`pointer-events-none ${className}`}>
			<LoadingSpinner size={size} />
		</Button>
	);

	// Use existing floating notification system
	const { show, hide } = useFloatingNotification(id, loadingContent, {
		priority: 'critical',
		persistent: true,
		collisionDetection: false,
	});

	// Show/hide based on loading state
	useEffect(() => {
		if (isLoading) {
			show();
		} else {
			hide();
		}
	}, [isLoading, show, hide]);

	// This component doesn't render anything directly
	// The floating UI system handles the rendering
	return null;
}

// ============================================================================
// FLOATING SCOPED LOADING COMPONENT
// ============================================================================

interface FloatingScopedLoadingProps extends FloatingLoadingProps {
	scope: string;
	loadingKey: string;
}

export function FloatingScopedLoading({
	scope,
	loadingKey,
	id,
	...props
}: FloatingScopedLoadingProps) {
	const { getLoading } = useScopedLoading(scope);
	const isLoading = getLoading(loadingKey);

	// Create simple disabled spinner button
	const loadingContent = (
		<Button disabled className={`pointer-events-none ${props.className || ''}`}>
			<LoadingSpinner size={props.size || 'md'} />
		</Button>
	);

	// Use existing floating notification system
	const { show, hide } = useFloatingNotification(
		id || `scoped-loading-${scope}-${loadingKey}`,
		loadingContent,
		{
			priority: 'medium',
			animation: { type: 'slide', duration: 250 },
			collisionDetection: true,
		}
	);

	useEffect(() => {
		if (isLoading) {
			show();
		} else {
			hide();
		}
	}, [isLoading, show, hide]);

	return null;
}

// ============================================================================
// FLOATING LOADING MANAGER
// ============================================================================

export function FloatingLoadingManager() {
	return (
		<>
			{/* Global loading indicator với priority cao nhất */}
			<FloatingLoading id="global-loading" />
		</>
	);
}
