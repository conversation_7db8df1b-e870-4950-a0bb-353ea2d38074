'use client';

import { useFloatingNotification } from '@/hooks/use-floating-ui';
import { useAllLoadingStates } from '@/contexts/loading-context';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { Button } from '@/components/ui/button';
import { useEffect } from 'react';

// ============================================================================
// FLOATING LOADING COMPONENT
// ============================================================================

interface FloatingLoadingProps {
	id?: string;
	className?: string;
}

export function FloatingLoading({ id = 'global-loading', className = '' }: FloatingLoadingProps) {
	const { isLoading, loadingStates } = useAllLoadingStates();

	// Check if any loading state is active (global or any scoped)
	const hasAnyLoading = isLoading || Object.values(loadingStates).some((state) => state);

	// Create loading button with same style as feedback/settings buttons
	const loadingContent = (
		<Button
			disabled
			size="icon"
			className={`h-14 w-14 rounded-full shadow-lg pointer-events-none opacity-70 ${className}`}
		>
			<LoadingSpinner size="md" />
		</Button>
	);

	// Use existing floating notification system
	const { show, hide } = useFloatingNotification(id, loadingContent, {
		priority: 'critical',
		persistent: true,
		collisionDetection: false,
	});

	// Show/hide based on ANY loading state
	useEffect(() => {
		if (hasAnyLoading) {
			show();
		} else {
			hide();
		}
	}, [hasAnyLoading, show, hide]);

	// This component doesn't render anything directly
	// The floating UI system handles the rendering
	return null;
}

// ============================================================================
// FLOATING LOADING MANAGER
// ============================================================================

export function FloatingLoadingManager() {
	return (
		<>
			{/* Global loading indicator - hiển thị khi bất cứ loading state nào active */}
			<FloatingLoading id="global-loading" />
		</>
	);
}
