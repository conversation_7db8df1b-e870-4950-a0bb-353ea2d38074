'use client';

import { useFloatingNotification } from '@/hooks/use-floating-ui';
import { useAllLoadingStates } from '@/contexts/loading-context';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { Button } from '@/components/ui/button';
import { useEffect } from 'react';

// ============================================================================
// FLOATING LOADING COMPONENT
// ============================================================================

interface FloatingLoadingProps {
	id?: string;
	size?: 'sm' | 'md' | 'lg';
	className?: string;
}

export function FloatingLoading({
	id = 'global-loading',
	size = 'md',
	className = '',
}: FloatingLoadingProps) {
	const { isLoading, loadingStates } = useAllLoadingStates();

	// Check if any loading state is active (global or any scoped)
	const hasAnyLoading = isLoading || Object.values(loadingStates).some((state) => state);

	// Create simple disabled spinner button
	const loadingContent = (
		<Button disabled className={`pointer-events-none ${className}`}>
			<LoadingSpinner size={size} />
		</Button>
	);

	// Use existing floating notification system
	const { show, hide } = useFloatingNotification(id, loadingContent, {
		priority: 'critical',
		persistent: true,
		collisionDetection: false,
	});

	// Show/hide based on ANY loading state
	useEffect(() => {
		if (hasAnyLoading) {
			show();
		} else {
			hide();
		}
	}, [hasAnyLoading, show, hide]);

	// This component doesn't render anything directly
	// The floating UI system handles the rendering
	return null;
}

// ============================================================================
// FLOATING LOADING MANAGER
// ============================================================================

export function FloatingLoadingManager() {
	return (
		<>
			{/* Global loading indicator - hiển thị khi bất cứ loading state nào active */}
			<FloatingLoading id="global-loading" />
		</>
	);
}
