# Floating Loading System

## Overview

The Floating Loading System uses the existing Floating UI notification system (same as settings, feedback, and guidance) to provide consistent loading indicators throughout the application. Instead of traditional inline loading spinners, this system uses the same floating notification pattern with disabled spinner buttons.

## Key Features

-   **Uses Existing Floating Notification System**: Same system as settings, feedback, and guidance
-   **Simple Disabled Button Design**: Uses disabled spinner buttons for consistent UI
-   **Multiple Loading Types**: Global, scoped, and custom loading indicators
-   **Consistent Positioning**: Uses default notification positioning (top-right)
-   **Collision Detection**: Automatic collision detection and resolution (except for global)
-   **Priority Management**: Different priority levels for loading indicators
-   **Animation Support**: Smooth animations with Framer Motion

## Components

### FloatingLoading

Basic floating loading component that automatically shows/hides based on global loading state.

```tsx
import { FloatingLoading } from '@/components/floating-ui';

<FloatingLoading id="my-loading" size="md" className="custom-class" />;
```

### FloatingScopedLoading

Scoped loading component for specific operations.

```tsx
import { FloatingScopedLoading } from '@/components/floating-ui';

<FloatingScopedLoading
	scope="collections"
	loadingKey="fetch"
	message="Fetching collections..."
	position="top-right"
	size="lg"
/>;
```

### FloatingLoadingManager

Global manager that handles the default loading indicators.

```tsx
import { FloatingLoadingManager } from '@/components/floating-ui';

// Already included in the main layout
<FloatingLoadingManager />;
```

## Hooks

### useFloatingLoading

Hook for global loading management with floating UI.

```tsx
import { useFloatingLoading } from '@/components/floating-ui';

function MyComponent() {
	const loading = useFloatingLoading({
		size: 'md',
		priority: 'critical', // Default for global loading
	});

	const handleAction = async () => {
		loading.setLoading(true);
		try {
			await someAsyncOperation();
		} finally {
			loading.setLoading(false);
		}
	};

	return <button onClick={handleAction}>Start Action</button>;
}
```

### useFloatingScopedLoading

Hook for scoped loading management.

```tsx
import { useFloatingScopedLoading } from '@/components/floating-ui';

function CollectionComponent() {
	const loading = useFloatingScopedLoading('collections', 'fetch', {
		message: 'Loading collections...',
		position: 'top-right',
		priority: 'medium',
	});

	const fetchCollections = async () => {
		loading.setLoading(true);
		try {
			const data = await api.getCollections();
			// Handle data
		} finally {
			loading.setLoading(false);
		}
	};

	return (
		<div>
			<button onClick={fetchCollections}>Fetch Collections</button>
			{loading.isLoading && <p>Loading state: Active</p>}
		</div>
	);
}
```

### useCustomFloatingLoading

Hook for custom loading content and behavior.

```tsx
import { useCustomFloatingLoading } from '@/components/floating-ui';
import { LoadingSpinner } from '@/components/ui/loading-spinner';

function CustomLoadingExample() {
	const customContent = (
		<div className="flex items-center gap-3 p-4 rounded-lg bg-gradient-to-r from-blue-500 to-purple-600 text-white shadow-xl">
			<LoadingSpinner size="md" className="border-white border-t-transparent" />
			<div>
				<div className="font-semibold">Custom Loading</div>
				<div className="text-sm opacity-90">Please wait...</div>
			</div>
		</div>
	);

	const loading = useCustomFloatingLoading('custom-loading', customContent, {
		position: 'center',
		priority: 'critical',
		persistent: true,
	});

	return (
		<div>
			<button onClick={loading.show}>Show Custom Loading</button>
			<button onClick={loading.hide}>Hide Custom Loading</button>
			<button onClick={loading.toggle}>Toggle Custom Loading</button>
		</div>
	);
}
```

## Configuration Options

### Position Options

-   `top-left`: Top-left corner
-   `top-right`: Top-right corner
-   `bottom-left`: Bottom-left corner
-   `bottom-right`: Bottom-right corner (default)
-   `center`: Center of screen

### Size Options

-   `sm`: Small spinner (16x16px)
-   `md`: Medium spinner (32x32px, default)
-   `lg`: Large spinner (48x48px)

### Priority Options

-   `low`: Z-index 1000
-   `medium`: Z-index 1100 (default for scoped)
-   `high`: Z-index 1200
-   `critical`: Z-index 1300 (default for global - luôn ở trên cùng)

## Integration with Existing Loading Context

The floating loading system seamlessly integrates with the existing loading context:

```tsx
import { useLoading } from '@/contexts/loading-context';

function ExistingComponent() {
	const { setLoading } = useLoading();

	const handleAction = async () => {
		// This will automatically show the floating loading indicator
		setLoading(true);
		try {
			await someOperation();
		} finally {
			setLoading(false);
		}
	};

	return <button onClick={handleAction}>Action</button>;
}
```

## Migration from Old System

The old loading system with inline spinners is still supported, but the new floating system provides these advantages:

1. **Better UX**: Loading indicators don't affect layout
2. **Consistent Positioning**: All loading indicators appear in predictable locations
3. **Collision Management**: Multiple loading indicators are automatically managed
4. **Priority System**: Important loading operations can take precedence
5. **Animation**: Smooth enter/exit animations

## Best Practices

1. **Use Global Loading** for application-wide operations (automatically uses `critical` priority to stay on top)
2. **Use Scoped Loading** for specific feature operations (uses `medium` priority by default)
3. **Use Custom Loading** for special cases requiring unique styling
4. **Set Appropriate Priorities** to ensure important loading indicators are visible
5. **Keep Messages Concise** for better user experience
6. **Use Persistent Loading** only for critical operations that shouldn't be hidden by collisions
7. **Global Loading Priority**: Global loading always uses `critical` priority and disables collision detection to ensure it's always visible

## Examples

See `/test-floating-loading` page for live examples and testing of all floating loading features.
